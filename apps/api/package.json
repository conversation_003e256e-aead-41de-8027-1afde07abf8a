{"name": "@limico/api", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc -p tsconfig.json", "start": "node dist/index.js", "test": "vitest run", "test:watch": "vitest", "prisma": "prisma", "db:migrate": "prisma migrate dev", "db:generate": "prisma generate", "cleanup:temp-users": "tsx scripts/cleanup-temp-users.ts"}, "dependencies": {"@clerk/express": "^1.7.29", "@prisma/client": "^6.15.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "svix": "^1.76.1", "zod": "^3.25.6"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^22.7.4", "@types/supertest": "^6.0.3", "prisma": "^6.15.0", "supertest": "^7.1.4", "tsx": "^4.19.1", "typescript": "^5.5.4", "vitest": "^2.0.5"}}