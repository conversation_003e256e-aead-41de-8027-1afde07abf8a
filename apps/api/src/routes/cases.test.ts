import { describe, it, expect } from 'vitest';
import { pickLatest, classifyRole } from './cases';

// Minimal stubs to match Project shape
interface ProjectStub {
  id: string;
  caseId: string;
  parentTaskId: string | null;
  status: string;
  createdAt: Date;
  updatedAt: Date;
  title: string;
  client: string;
  salesAmount: number;
}
const mkProject = (over: Partial<ProjectStub> = {}): ProjectStub => ({
  id: over.id || 'p1',
  caseId: over.caseId || 'c1',
  parentTaskId: over.parentTaskId ?? null,
  status: over.status || 'lead',
  createdAt: over.createdAt ? new Date(over.createdAt) : new Date('2024-01-01T00:00:00Z'),
  updatedAt: over.updatedAt ? new Date(over.updatedAt) : new Date('2024-01-01T00:00:00Z'),
  title: 't', client: 'cl', salesAmount: 0,
});

describe('pickLatest', () => {
  it('returns the most recently updated item', () => {
    const a = mkProject({ id: 'a', updatedAt: new Date('2024-01-01T00:00:00Z') });
    const b = mkProject({ id: 'b', updatedAt: new Date('2024-01-02T00:00:00Z') });
    const c = mkProject({ id: 'c', updatedAt: new Date('2023-12-31T23:59:59Z') });
    const got = pickLatest([a,b,c])!;
    expect(got.id).toBe('b');
  });

  it('returns undefined for empty array', () => {
    expect(pickLatest([] as ProjectStub[])).toBeUndefined();
  });
});

describe('classifyRole', () => {
  it('classifies sales when no parentTaskId', () => {
    const p = mkProject({ parentTaskId: null, status: 'lead' });
    // classifyRole expects a Prisma Project; the stub satisfies required fields
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    expect(classifyRole(p as any)).toBe('sales');
  });
  it('classifies designer based on statuses', () => {
    const p = mkProject({ parentTaskId: 'root', status: '3d' });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    expect(classifyRole(p as any)).toBe('designer');
  });
  it('classifies supervisor based on statuses', () => {
    const p = mkProject({ parentTaskId: 'root', status: 'inprogress' });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    expect(classifyRole(p as any)).toBe('supervisor');
  });
});

