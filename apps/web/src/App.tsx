import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ClerkProvider } from "@clerk/clerk-react";
import { UserProvider } from "./contexts/UserContext";
import Index from "./pages/Index";
import CaseHistory from "./pages/CaseHistory";
import CaseDetails from "./pages/CaseDetails";
import ProjectDetails from "./pages/ProjectDetails";
import SignIn from "./pages/SignIn";
import SignUp from "./pages/SignUp";
import UserManagement from "./pages/UserManagement";
import UserProfile from "./pages/UserProfile";
import ProtectedRoute from "./components/ProtectedRoute";
import ErrorBoundary from "./components/ErrorBoundary";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

// Get the publishable key from environment variables
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY;

if (!clerkPubKey) {
  console.warn("Missing Clerk Publishable Key - authentication features will be limited");
}

const App = () => (
  <ErrorBoundary>
    <ClerkProvider publishableKey={clerkPubKey || ""}>
      <UserProvider>
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
      <Toaster />
      <Sonner />
      <BrowserRouter>
        <Routes>
          <Route path="/" element={
            <ProtectedRoute>
              <Index />
            </ProtectedRoute>
          } />
          <Route path="/case-history" element={
            <ProtectedRoute>
              <CaseHistory />
            </ProtectedRoute>
          } />
          <Route path="/case-history/:caseId" element={
            <ProtectedRoute>
              <CaseDetails />
            </ProtectedRoute>
          } />
          <Route path="/projects/:id" element={
            <ProtectedRoute>
              <ProjectDetails />
            </ProtectedRoute>
          } />
          <Route path="/users" element={
            <ProtectedRoute requiredRole="owner">
              <UserManagement />
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <UserProfile />
            </ProtectedRoute>
          } />
          <Route path="/sign-in/*" element={<SignIn />} />
          <Route path="/sign-up/*" element={<SignUp />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
          </TooltipProvider>
        </QueryClientProvider>
      </UserProvider>
    </ClerkProvider>
  </ErrorBoundary>
);

export default App;
