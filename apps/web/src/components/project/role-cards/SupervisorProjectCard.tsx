import {
  Project,
  User,
  UserRole,
  ProjectStatus,
  SupervisorSubTask,
  canModifyTask,
  is<PERSON>anager,
  isOwner,
} from "@/types/project";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/datetime";
import { Progress } from "@/components/ui/progress";
import { StatusBadge } from "@/components/StatusBadge";
import { statusToTone, shouldHideActionButtons } from "@/lib/status";
import { Calendar, User as UserIcon, Eye, Play, List, CheckSquare } from "lucide-react";

import { SupervisorAssignDialog } from "@/components/SupervisorAssignDialog";
import { SubtaskSelectionDialog } from "@/components/SubtaskSelectionDialog";
import { useState, useEffect } from "react";
import { SupervisorPhaseDialog, PhaseStateRecord } from "@/components/SupervisorPhaseDialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "sonner";
import { Api } from "@/lib/api";
import { sortPhasesByCanonicalOrder } from "@/lib/supervisor-subtasks";

interface SupervisorProjectCardProps {
  project: Project;
  userRole: UserRole;
  currentUserId: string;
  onStatusUpdate: (
    projectId: string,
    newStatus: ProjectStatus,
    newSubStatus?: undefined,
    phaseCompletedAt?: string,
    phaseKey?: string
  ) => void;
  onViewDetails: (project: Project) => void;
  onAssignTask?: (projectId: string, assigneeId: string, phases?: string[]) => void;
  onSubtaskSelection?: (projectId: string, selectedTasks: string[]) => void;
  onDeleteProject?: (projectId: string) => void;
  onProjectUpdate?: (projectId: string, updatedProject: Project) => void;
  availableUsers?: User[];
}

export const SupervisorProjectCard = ({
  project,
  userRole,
  currentUserId,
  onStatusUpdate,
  onViewDetails,
  onAssignTask,
  onSubtaskSelection,
  onDeleteProject, // kept in props signature for compatibility
  onProjectUpdate,
  availableUsers = [],
}: SupervisorProjectCardProps) => {
  const [phaseStates, setPhaseStates] = useState<PhaseStateRecord>({});
  const selectedPhases = sortPhasesByCanonicalOrder(project.supervisorSelectedPhases || []) as SupervisorSubTask[];

  // Initialize and update phaseStates from project data
  useEffect(() => {
    const projectPhaseStates = (project as any).supervisorPhaseStates as PhaseStateRecord | undefined;
    if (projectPhaseStates) {
      setPhaseStates(projectPhaseStates);
    }
  }, [project.id, (project as any).supervisorPhaseStates]);

  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [showSubtaskDialog, setShowSubtaskDialog] = useState(false);
  const [showProgressDialog, setShowProgressDialog] = useState(false);

  // Project Start functionality
  const [showProjectStartDialog, setShowProjectStartDialog] = useState(false);
  const [selectedDuration, setSelectedDuration] = useState<string>("45"); // Default to 45 days

  const canModify = canModifyTask(userRole, project.assignedTo || "", currentUserId);
  const supervisorStatuses: ProjectStatus[] = ["supervisor_pending_assign", "created", "inprogress", "completed"];

  const legacySupervisorSubtasks = [
    "floor_protection",
    "plaster_ceiling",
    "spc",
    "first_painting",
    "carpentry_measure",
    "measure_others",
    "carpentry_install",
    "quartz_measure",
    "quartz_install",
    "glass_measure",
    "glass_install",
    "final_wiring",
    "final_painting",
    "install_others",
    "plumbing",
    "cleaning",
    "defects",
  ] as const;

  const isSupervisorCardActive =
    (project.status === "inprogress" ||
      (Array.isArray(project.supervisorSelectedPhases) && project.supervisorSelectedPhases.length > 0) ||
      (legacySupervisorSubtasks as readonly string[]).includes(project.status as string)) &&
    project.status !== "supervisor_pending_assign" &&
    project.status !== "created" &&
    project.status !== "completed";

  // Owners and managers can always assign
  const canAssignTask = isOwner(userRole) || isManager(userRole);

  // use shared StatusBadge for colors

  // Compute progress from new per-phase states if available; fallback to legacy dates
  const computeProgressPercentage = (): number => {
    // If the overall supervisor task is marked completed, show 100% regardless of per-phase data
    if (project.status === 'completed') return 100;

    let selected = (project.supervisorSelectedPhases || []) as SupervisorSubTask[];

    const localStates = phaseStates && Object.keys(phaseStates).length > 0 ? phaseStates : undefined;
    const projectStates = (project as any).supervisorPhaseStates as Record<string, { status: string }> | undefined;

    // If no selected phases, but we have states (local or server), treat those keys as selected for progress purposes
    if (selected.length === 0 && (localStates || projectStates)) {
      const states = (localStates || projectStates)!;
      selected = Object.keys(states) as SupervisorSubTask[];
    }

    const total = selected.length;
    if (total === 0) return 0;

    if (localStates || projectStates) {
      const states = (localStates || projectStates)!;
      const completed = selected.filter((p) => states[p]?.status === 'complete').length;
      return (completed / total) * 100;
    }

    const dates = (project.supervisorPhaseDates as Record<string, string>) || {};
    const completed = selected.filter((p) => !!dates[p]).length;
    return (completed / total) * 100;
  };

  const progressPercentage = computeProgressPercentage();

  const handleSupervisorAssign = (assigneeId: string) => {
    if (onAssignTask) onAssignTask(project.id, assigneeId);
  };

  const handleSubtaskSelection = (selectedTasks: string[]) => {
    if (onSubtaskSelection) onSubtaskSelection(project.id, selectedTasks);
  };

  // Project Start functionality
  const handleProjectStart = async () => {
    if (!selectedDuration) {
      toast.error("Please select a duration");
      return;
    }

    const startDate = new Date();
    const handoverDate = new Date(startDate);
    handoverDate.setDate(startDate.getDate() + parseInt(selectedDuration));

    const startDateISO = startDate.toISOString().split('T')[0];
    const handoverDateISO = handoverDate.toISOString().split('T')[0];

    try {
      await Api.updateProject(project.id, {
        supervisorProjectStartDate: startDateISO,
        handoverDate: handoverDateISO,
        renovationPeriod: selectedDuration, // Store the number in backend
      });

      toast.success(`Project started! Handover date set to ${handoverDate.toLocaleDateString()}`);
      setShowProjectStartDialog(false);
      setSelectedDuration("45"); // Reset to default

      // Refresh the project data if there's a callback
      if (onStatusUpdate) {
        onStatusUpdate(project.id, project.status);
      }
    } catch (error) {
      toast.error("Failed to start project");
      console.error("Project start error:", error);
    }
  };

  const isProjectStarted = !!(project.supervisorProjectStartDate);
  const canStartProject = (isOwner(userRole) || isManager(userRole) || canModify) && !isProjectStarted && !!project.assignedTo;

  // Hide progress button if the task is not yet assigned or no phases selected
  const shouldHideProgress = !project.assignedTo || selectedPhases.length === 0;

  return (
    <Card className="hover:shadow-md transition-shadow flex flex-col h-full">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-lg font-semibold line-clamp-2">{project.title}</CardTitle>
          <div className="flex flex-col items-end gap-1">
            <StatusBadge status={project.status} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4 flex flex-col flex-1">
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <UserIcon className="h-4 w-4" />
            <span>{project.client}</span>
          </div>

          {project.assignedTo ? (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <UserIcon className="h-4 w-4" />
              <span>
                Assigned to: {
                  availableUsers?.find((u) => u.id === project.assignedTo)?.name || "Unknown"
                }
              </span>
            </div>
          ) : (
            <div className="flex items-center gap-2 text-sm text-orange-600">
              <UserIcon className="h-4 w-4" />
              <span>Unassigned</span>
            </div>
          )}

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>Created: {formatDate(project.createdAt)}</span>
          </div>

          {/* Supervisor Project Fields */}
          {project.renovationPeriod && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>Renovation Period: {
                // If it's a number, format as "X working days", otherwise show as-is
                /^\d+$/.test(project.renovationPeriod)
                  ? `${project.renovationPeriod} working days`
                  : project.renovationPeriod
              }</span>
            </div>
          )}

          {project.handoverDate && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>Handover Date: {formatDate(project.handoverDate)}</span>
            </div>
          )}

          {project.supervisorProjectStartDate && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>Start Date: {formatDate(project.supervisorProjectStartDate)}</span>
            </div>
          )}

          {project.remarks && (
            <div className="text-sm">
              <span className="text-muted-foreground">Remarks: </span>
              <span
                className="text-foreground line-clamp-2 max-w-full overflow-hidden text-ellipsis"
                style={{
                  display: '-webkit-box',
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: 'vertical',
                  maxHeight: '2.5rem'
                }}
                title={project.remarks}
              >
                {project.remarks}
              </span>
            </div>
          )}
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Progress</span>
            <span className="font-medium">{Math.round(progressPercentage)}%</span>
          </div>
          <Progress value={progressPercentage} tone={statusToTone(progressPercentage === 100 ? 'completed' : project.status)} className="h-2" />
        </div>

        <div className="flex-1" />

        <div className="flex flex-wrap gap-2 pt-2 mt-auto w-full">
          {/* Always show Details button */}
          <Button variant="outline" size="sm" className="flex-1 justify-center" onClick={() => onViewDetails(project)} title="Details" aria-label="Details">
            <Eye className="h-4 w-4" />
          </Button>

          {/* Primary action - most relevant for current state */}
          {(() => {
            // Assign button (highest priority when unassigned)
            if (!shouldHideActionButtons(project.status) && (isOwner(userRole) || isManager(userRole)) && !project.assignedTo) {
              return (
                <Button variant="default" size="sm" onClick={() => setShowAssignDialog(true)} className="flex-1">
                  <UserIcon className="h-4 w-4 mr-1" />
                  Assign
                </Button>
              );
            }

            // Manage Subtasks (when project is assigned)
            if (project.assignedTo && (isOwner(userRole) || isManager(userRole) || canModify)) {
              return selectedPhases.length > 0 ? (
                <Button variant="default" size="sm" className="flex-1 justify-center" onClick={() => setShowSubtaskDialog(true)} title="Manage Subtasks" aria-label="Manage Subtasks">
                  <List className="h-4 w-4" />
                </Button>
              ) : (
                <Button variant="default" size="sm" className="flex-1" onClick={() => setShowSubtaskDialog(true)}>
                  <List className="h-4 w-4 mr-1" />
                  Manage Subtasks
                </Button>
              );
            }

            return null;
          })()}

          {/* Secondary actions shown inline (no dropdown) */}
          {canStartProject && (
            <Button variant="outline" size="sm" className="flex-1" onClick={() => setShowProjectStartDialog(true)}>
              <Play className="h-4 w-4 mr-1" />
              Start
            </Button>
          )}

          {(canModify || isOwner(userRole) || isManager(userRole)) && !shouldHideProgress && (
            <Button variant="default" size="sm" className="flex-1" onClick={() => setShowProgressDialog(true)}>
              <CheckSquare className="h-4 w-4 mr-1" />
              Update Subtasks
            </Button>
          )}
        </div>
      </CardContent>

      <SupervisorPhaseDialog
        open={showProgressDialog}
        onOpenChange={setShowProgressDialog}
        selectedPhases={selectedPhases}
        phaseStates={phaseStates}
        onChange={setPhaseStates}
        onTransition={async (phaseKey, to, additionalData) => {
          try {
            const updated = await Api.transitionSupervisorPhase(project.id, {
              phaseKey,
              to,
              ...additionalData
            });
            if ((updated as any)?.supervisorPhaseStates) {
              setPhaseStates((updated as any).supervisorPhaseStates as PhaseStateRecord);
            }
            // Update the main projects list with the latest project data
            if (onProjectUpdate) {
              onProjectUpdate(project.id, updated);
            }
            if (updated.status === 'completed') {
              // Inform parent that project moved to completed
              onStatusUpdate(project.id, 'completed');
            }
          } catch (e) {
            console.error('Failed to transition phase', e);
          }
        }}
        onUpdate={async (phaseKey, data) => {
          try {
            const updated = await Api.updateSupervisorPhase(project.id, {
              phaseKey,
              ...data
            });
            if ((updated as any)?.supervisorPhaseStates) {
              setPhaseStates((updated as any).supervisorPhaseStates as PhaseStateRecord);
            }
            // Update the main projects list with the latest project data
            if (onProjectUpdate) {
              onProjectUpdate(project.id, updated);
            }
          } catch (e) {
            console.error('Failed to update phase data', e);
          }
        }}
      />

      {(userRole === "manager" || userRole === "owner") && (
        <SupervisorAssignDialog
          open={showAssignDialog}
          onOpenChange={setShowAssignDialog}
          designers={availableUsers?.filter((u) => u.role === "supervisor") || []}
          onConfirm={handleSupervisorAssign}
        />
      )}

      <SubtaskSelectionDialog
        open={showSubtaskDialog}
        onOpenChange={setShowSubtaskDialog}
        onConfirm={handleSubtaskSelection}
        projectId={project.id}
        initialSelected={project.supervisorSelectedPhases || []}
      />

      {/* Project Start Dialog */}
      {showProjectStartDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-96 max-w-md">
            <h3 className="text-lg font-semibold mb-4">Start Project</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Select the project duration. Start date will be set to today, and handover date will be calculated automatically.
            </p>

            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium">Duration (days)</label>
                <Select value={selectedDuration} onValueChange={setSelectedDuration}>
                  <SelectTrigger className="w-full mt-1">
                    <SelectValue placeholder="Select duration" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="30">30 days</SelectItem>
                    <SelectItem value="45">45 days</SelectItem>
                    <SelectItem value="60">60 days</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {selectedDuration && (
                <div className="text-sm text-muted-foreground">
                  <p>Start Date: {new Date().toLocaleDateString()}</p>
                  <p>Handover Date: {new Date(Date.now() + parseInt(selectedDuration) * 24 * 60 * 60 * 1000).toLocaleDateString()}</p>
                </div>
              )}
            </div>

            <div className="flex gap-2 mt-6">
              <Button
                variant="outline"
                onClick={() => {
                  setShowProjectStartDialog(false);
                  setSelectedDuration("45"); // Reset to default
                }}
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleProjectStart}
                disabled={!selectedDuration}
                className="flex-1"
              >
                Start Project
              </Button>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

